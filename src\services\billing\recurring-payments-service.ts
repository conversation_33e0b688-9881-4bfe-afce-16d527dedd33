/**
 * Serviço para processamento de pagamentos recorrentes pendentes
 * Usado pelos cron jobs para automatizar a criação de pagamentos que não foram criados devido a pagamentos antecipados
 */

import { createClient } from '@/services/supabase/server'

export interface RecurringPaymentsResult {
  success: boolean
  error?: string
  data?: {
    processedCount: number
    errorCount: number
    totalAnalyzed: number
    processedAt: string
    results: Array<{
      paymentId: string
      membershipId: string
      success: boolean
      message: string
      nextPaymentId?: string
    }>
  }
}

export interface PendingRecurringPaymentsResult {
  success: boolean
  error?: string
  data?: {
    pendingCount: number
    pendingPayments: Array<{
      paymentId: string
      membershipId: string
      dueDate: string
      paidAt: string
      planTitle: string
      frequency: string
      daysOverdue: number
    }>
  }
}

/**
 * Processa pagamentos recorrentes pendentes usando função do Supabase
 * Cria pagamentos que deveriam ter sido criados mas não foram devido a pagamentos antecipados
 */
export async function processRecurringPayments(tenantId?: string): Promise<RecurringPaymentsResult> {
  try {
    const supabase = await createClient()

    console.log(`🔄 Processando pagamentos recorrentes pendentes para ${tenantId ? `tenant ${tenantId}` : 'todos os tenants'}...`)

    // Chamar função do Supabase para processar pagamentos recorrentes pendentes
    const { data: result, error } = await supabase.rpc('process_pending_recurring_payments')

    if (error) {
      console.error('❌ Erro ao chamar função do Supabase:', error)
      return {
        success: false,
        error: `Erro ao processar pagamentos recorrentes: ${error.message}`
      }
    }

    if (!result) {
      return {
        success: false,
        error: 'Nenhum resultado retornado pela função'
      }
    }

    // Verificar se houve erro interno na função
    if (!result.success) {
      console.error('❌ Erro interno na função:', result.error)
      return {
        success: false,
        error: result.error || 'Erro interno na função'
      }
    }

    const processedCount = result.processed_count || 0
    const errorCount = result.error_count || 0
    const totalAnalyzed = result.total_analyzed || 0

    console.log(`✅ Processamento de pagamentos recorrentes concluído:`)
    console.log(`  - Total analisados: ${totalAnalyzed}`)
    console.log(`  - Processados com sucesso: ${processedCount}`)
    console.log(`  - Erros: ${errorCount}`)

    // Log detalhado dos resultados
    if (result.results && Array.isArray(result.results)) {
      result.results.forEach((item: any, index: number) => {
        if (item.success) {
          console.log(`  ✅ [${index + 1}] Pagamento ${item.payment_id} processado - Próximo: ${item.next_payment_id}`)
        } else {
          console.log(`  ❌ [${index + 1}] Pagamento ${item.payment_id} falhou: ${item.message}`)
        }
      })
    }

    return {
      success: true,
      data: {
        processedCount,
        errorCount,
        totalAnalyzed,
        processedAt: result.processed_at,
        results: result.results || []
      }
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido'
    console.error('💥 Erro crítico ao processar pagamentos recorrentes:', error)
    
    return {
      success: false,
      error: `Erro crítico: ${errorMessage}`
    }
  }
}

/**
 * Verifica quais pagamentos recorrentes precisam ser processados
 * Útil para monitoramento e debug
 */
export async function checkPendingRecurringPayments(tenantId?: string): Promise<PendingRecurringPaymentsResult> {
  try {
    const supabase = await createClient()

    console.log(`🔍 Verificando pagamentos recorrentes pendentes para ${tenantId ? `tenant ${tenantId}` : 'todos os tenants'}...`)

    // Chamar função do Supabase para verificar pagamentos pendentes
    const { data: result, error } = await supabase.rpc('check_pending_recurring_payments')

    if (error) {
      console.error('❌ Erro ao chamar função do Supabase:', error)
      return {
        success: false,
        error: `Erro ao verificar pagamentos pendentes: ${error.message}`
      }
    }

    if (!result) {
      return {
        success: false,
        error: 'Nenhum resultado retornado pela função'
      }
    }

    // Verificar se houve erro interno na função
    if (result.error) {
      console.error('❌ Erro interno na função:', result.error)
      return {
        success: false,
        error: result.error
      }
    }

    const pendingCount = result.pending_count || 0
    const pendingPayments = result.pending_payments || []

    console.log(`📋 Verificação concluída:`)
    console.log(`  - Pagamentos pendentes: ${pendingCount}`)

    if (pendingPayments.length > 0) {
      console.log(`📝 Detalhes dos pagamentos pendentes:`)
      pendingPayments.forEach((payment: any, index: number) => {
        console.log(`  [${index + 1}] ${payment.plan_title} - Venceu há ${payment.days_overdue} dias`)
        console.log(`      Pagamento: ${payment.payment_id}`)
        console.log(`      Membership: ${payment.membership_id}`)
        console.log(`      Vencimento: ${payment.due_date}`)
        console.log(`      Pago em: ${payment.paid_at}`)
      })
    }

    return {
      success: true,
      data: {
        pendingCount,
        pendingPayments
      }
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido'
    console.error('💥 Erro crítico ao verificar pagamentos pendentes:', error)
    
    return {
      success: false,
      error: `Erro crítico: ${errorMessage}`
    }
  }
}

/**
 * Processa pagamentos recorrentes pendentes com retry automático
 * Versão com sistema de retry para maior robustez
 */
export async function processRecurringPaymentsWithRetry(
  tenantId?: string,
  maxRetries: number = 3
): Promise<RecurringPaymentsResult> {
  let lastError: string | undefined
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    console.log(`🔄 Tentativa ${attempt}/${maxRetries} de processar pagamentos recorrentes...`)
    
    const result = await processRecurringPayments(tenantId)
    
    if (result.success) {
      if (attempt > 1) {
        console.log(`✅ Processamento bem-sucedido na tentativa ${attempt}`)
      }
      return result
    }
    
    lastError = result.error
    console.warn(`⚠️ Tentativa ${attempt} falhou: ${result.error}`)
    
    // Aguardar antes da próxima tentativa (exceto na última)
    if (attempt < maxRetries) {
      const waitTime = attempt * 1000 // 1s, 2s, 3s...
      console.log(`⏳ Aguardando ${waitTime}ms antes da próxima tentativa...`)
      await new Promise(resolve => setTimeout(resolve, waitTime))
    }
  }
  
  return {
    success: false,
    error: `Falha após ${maxRetries} tentativas. Último erro: ${lastError}`
  }
}
